import { useState } from "react";
import { formatDate } from "../lib/utils";
import { FileText, Check, X, Clock } from "lucide-react";

export default function ApplicationCard({ application, user, onUpdate }) {
  const [loading, setLoading] = useState(false);

  const handleAction = async (action) => {
    setLoading(true);
    try {
      const res = await fetch(`/api/applications/${application.id}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ action }),
      });

      if (res.ok) {
        onUpdate();
      }
    } catch (error) {
      console.error("Failed to update application:", error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = () => {
    const statusConfig = {
      pending: { color: "bg-yellow-100 text-yellow-800", icon: Clock },
      accepted: { color: "bg-green-100 text-green-800", icon: Check },
      rejected: { color: "bg-red-100 text-red-800", icon: X },
      in_progress: { color: "bg-blue-100 text-blue-800", icon: FileText },
      completed: { color: "bg-purple-100 text-purple-800", icon: Check },
    };

    const config = statusConfig[application.status] || statusConfig.pending;
    const Icon = config.icon;

    return (
      <span
        className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm ${config.color}`}
      >
        <Icon size={14} />
        {application.status.replace("_", " ")}
      </span>
    );
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex justify-between items-start mb-4">
        <div>
          <h4 className="text-lg font-semibold mb-1">
            {user.user_type === "seeker"
              ? application.referrer_name
              : application.job_title}
          </h4>
          <p className="text-gray-600 text-sm">
            {user.user_type === "seeker"
              ? `Applied on ${formatDate(application.created_at)}`
              : `By ${application.seeker_name}`}
          </p>
        </div>
        {getStatusBadge()}
      </div>

      {application.message && (
        <p className="text-gray-700 mb-4">{application.message}</p>
      )}

      {application.company_name && (
        <p className="text-sm text-gray-600 mb-4">
          Company:{" "}
          <span className="font-medium">{application.company_name}</span>
        </p>
      )}

      {application.status === "pending" && user.user_type === "seeker" && (
        <div className="flex gap-2">
          <button
            onClick={() => handleAction("accept")}
            disabled={loading}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors disabled:opacity-50"
          >
            Accept
          </button>
          <button
            onClick={() => handleAction("reject")}
            disabled={loading}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors disabled:opacity-50"
          >
            Reject
          </button>
        </div>
      )}

      {application.status === "accepted" && (
        <button
          onClick={() => router.push(`/contract/${application.id}`)}
          className="px-4 py-2 bg-black text-white rounded hover:bg-gray-800 transition-colors"
        >
          View Contract
        </button>
      )}
    </div>
  );
}
