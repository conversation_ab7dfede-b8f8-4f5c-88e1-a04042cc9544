import { useState } from "react";
import { X } from "lucide-react";

export default function JobForm({ onSubmit, onCancel, initialData = null }) {
  const [formData, setFormData] = useState({
    title: initialData?.title || "",
    description: initialData?.description || "",
    jobRole: initialData?.job_role || "software_developer",
    skills: initialData?.skills || "",
    experienceYears: initialData?.experience_years || "",
    desiredCompanies: initialData?.desired_companies || "",
    paymentType: initialData?.payment_type || "percentage",
    paymentPercentage: initialData?.payment_percentage || "",
    paymentFixed: initialData?.payment_fixed || "",
    visibility: initialData?.visibility || "public",
  });

  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);

  const validateForm = () => {
    const newErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = "Title is required";
    }

    if (!formData.description.trim()) {
      newErrors.description = "Description is required";
    }

    if (formData.paymentType === "percentage") {
      const percentage = parseFloat(formData.paymentPercentage);
      if (!percentage || percentage < 1 || percentage > 50) {
        newErrors.paymentPercentage = "Percentage must be between 1 and 50";
      }
    }

    if (formData.paymentType === "fixed") {
      const amount = parseFloat(formData.paymentFixed);
      if (!amount || amount < 1000) {
        newErrors.paymentFixed = "Amount must be at least ₹1,000";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    await onSubmit(formData);
    setLoading(false);
  };

  const jobRoles = [
    { value: "software_developer", label: "Software Developer" },
    { value: "designer", label: "Designer" },
    { value: "product_manager", label: "Product Manager" },
    { value: "data_scientist", label: "Data Scientist" },
    { value: "marketing", label: "Marketing" },
    { value: "sales", label: "Sales" },
    { value: "other", label: "Other" },
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="sticky top-0 bg-white border-b p-4 flex justify-between items-center">
          <h2 className="text-xl font-semibold">
            {initialData ? "Edit Job Post" : "Create New Job Post"}
          </h2>
          <button
            onClick={onCancel}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Title */}
          <div>
            <label className="block text-sm font-medium mb-2">
              Title <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) =>
                setFormData({ ...formData, title: e.target.value })
              }
              className={`w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-black ${
                errors.title ? "border-red-500" : ""
              }`}
              placeholder="e.g., Senior Software Developer needed at top tech companies"
            />
            {errors.title && (
              <p className="text-red-500 text-sm mt-1">{errors.title}</p>
            )}
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium mb-2">
              Description <span className="text-red-500">*</span>
            </label>
            <textarea
              rows={6}
              value={formData.description}
              onChange={(e) =>
                setFormData({ ...formData, description: e.target.value })
              }
              className={`w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-black ${
                errors.description ? "border-red-500" : ""
              }`}
              placeholder="Describe your background, what kind of role you're looking for..."
            />
            {errors.description && (
              <p className="text-red-500 text-sm mt-1">{errors.description}</p>
            )}
          </div>

          {/* Job Role */}
          <div>
            <label className="block text-sm font-medium mb-2">Job Role</label>
            <select
              value={formData.jobRole}
              onChange={(e) =>
                setFormData({ ...formData, jobRole: e.target.value })
              }
              className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
            >
              {jobRoles.map((role) => (
                <option key={role.value} value={role.value}>
                  {role.label}
                </option>
              ))}
            </select>
          </div>

          {/* Skills */}
          <div>
            <label className="block text-sm font-medium mb-2">Skills</label>
            <input
              type="text"
              value={formData.skills}
              onChange={(e) =>
                setFormData({ ...formData, skills: e.target.value })
              }
              className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
              placeholder="e.g., React, Node.js, Python, AWS"
            />
          </div>

          {/* Experience */}
          <div>
            <label className="block text-sm font-medium mb-2">
              Years of Experience
            </label>
            <input
              type="number"
              value={formData.experienceYears}
              onChange={(e) =>
                setFormData({ ...formData, experienceYears: e.target.value })
              }
              className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
              min="0"
              placeholder="0"
            />
          </div>

          {/* Target Companies */}
          <div>
            <label className="block text-sm font-medium mb-2">
              Target Companies (optional)
            </label>
            <input
              type="text"
              value={formData.desiredCompanies}
              onChange={(e) =>
                setFormData({ ...formData, desiredCompanies: e.target.value })
              }
              className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-black"
              placeholder="e.g., Google, Microsoft, Amazon"
            />
          </div>

          {/* Payment Type */}
          <div>
            <label className="block text-sm font-medium mb-2">
              Payment Type
            </label>
            <div className="space-y-2">
              <label className="flex items-center gap-2 p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                <input
                  type="radio"
                  name="paymentType"
                  value="percentage"
                  checked={formData.paymentType === "percentage"}
                  onChange={(e) =>
                    setFormData({ ...formData, paymentType: e.target.value })
                  }
                />
                <div>
                  <div className="font-medium">Percentage of salary</div>
                  <div className="text-sm text-gray-600">
                    Recurring monthly payment
                  </div>
                </div>
              </label>
              <label className="flex items-center gap-2 p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                <input
                  type="radio"
                  name="paymentType"
                  value="fixed"
                  checked={formData.paymentType === "fixed"}
                  onChange={(e) =>
                    setFormData({ ...formData, paymentType: e.target.value })
                  }
                />
                <div>
                  <div className="font-medium">Fixed amount</div>
                  <div className="text-sm text-gray-600">One-time payment</div>
                </div>
              </label>
            </div>
          </div>

          {/* Payment Amount */}
          {formData.paymentType === "percentage" && (
            <div>
              <label className="block text-sm font-medium mb-2">
                Percentage of Monthly Salary{" "}
                <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <input
                  type="number"
                  value={formData.paymentPercentage}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      paymentPercentage: e.target.value,
                    })
                  }
                  className={`w-full px-4 py-2 pr-8 border rounded-lg focus:outline-none focus:ring-2 focus:ring-black ${
                    errors.paymentPercentage ? "border-red-500" : ""
                  }`}
                  min="1"
                  max="50"
                  placeholder="10"
                />
                <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                  %
                </span>
              </div>
              {errors.paymentPercentage && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.paymentPercentage}
                </p>
              )}
            </div>
          )}

          {formData.paymentType === "fixed" && (
            <div>
              <label className="block text-sm font-medium mb-2">
                Fixed Amount <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                  ₹
                </span>
                <input
                  type="number"
                  value={formData.paymentFixed}
                  onChange={(e) =>
                    setFormData({ ...formData, paymentFixed: e.target.value })
                  }
                  className={`w-full pl-8 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-black ${
                    errors.paymentFixed ? "border-red-500" : ""
                  }`}
                  min="1000"
                  placeholder="50000"
                />
              </div>
              {errors.paymentFixed && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.paymentFixed}
                </p>
              )}
            </div>
          )}

          {/* Visibility */}
          <div>
            <label className="block text-sm font-medium mb-2">Visibility</label>
            <div className="space-y-2">
              <label className="flex items-start gap-2 p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                <input
                  type="radio"
                  name="visibility"
                  value="public"
                  checked={formData.visibility === "public"}
                  onChange={(e) =>
                    setFormData({ ...formData, visibility: e.target.value })
                  }
                  className="mt-1"
                />
                <div>
                  <div className="font-medium">Public</div>
                  <div className="text-sm text-gray-600">
                    Visible to everyone on the explore page
                  </div>
                </div>
              </label>
              <label className="flex items-start gap-2 p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                <input
                  type="radio"
                  name="visibility"
                  value="anonymous"
                  checked={formData.visibility === "anonymous"}
                  onChange={(e) =>
                    setFormData({ ...formData, visibility: e.target.value })
                  }
                  className="mt-1"
                />
                <div>
                  <div className="font-medium">Anonymous</div>
                  <div className="text-sm text-gray-600">
                    Your name will be hidden
                  </div>
                </div>
              </label>
              <label className="flex items-start gap-2 p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                <input
                  type="radio"
                  name="visibility"
                  value="private"
                  checked={formData.visibility === "private"}
                  onChange={(e) =>
                    setFormData({ ...formData, visibility: e.target.value })
                  }
                  className="mt-1"
                />
                <div>
                  <div className="font-medium">Private</div>
                  <div className="text-sm text-gray-600">
                    Only accessible via shareable link
                  </div>
                </div>
              </label>
            </div>
          </div>

          {/* Actions */}
          <div className="flex gap-4 pt-4 border-t">
            <button
              type="button"
              onClick={onCancel}
              className="flex-1 px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="flex-1 px-4 py-3 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors disabled:opacity-50"
            >
              {loading ? "Saving..." : initialData ? "Update" : "Create"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
