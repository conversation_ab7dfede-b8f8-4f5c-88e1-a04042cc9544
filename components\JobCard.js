import { useState } from "react";
import { useRouter } from "next/router";
import { formatCurrency, formatDate } from "../lib/utils";
import { Eye, EyeOff, Share2, Users, DollarSign, Calendar } from "lucide-react";

export default function JobCard({ job, user, isOwner = false }) {
  const router = useRouter();
  const [copied, setCopied] = useState(false);

  const handleShare = () => {
    const url = `${window.location.origin}/job/${job.shareable_link || job.id}`;
    navigator.clipboard.writeText(url);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const isAnonymous = job.visibility === "anonymous";
  const displayName =
    isAnonymous && !isOwner ? "Anonymous" : job.user_name || job.full_name;

  return (
    <div className="bg-white rounded-lg shadow hover:shadow-lg transition-shadow p-6 card-hover">
      <div className="flex justify-between items-start mb-4">
        <div className="flex-1">
          <h3 className="text-xl font-semibold mb-2">{job.title}</h3>
          <p className="text-gray-600 mb-4 line-clamp-2">{job.description}</p>
        </div>
        <div className="flex items-center gap-2 ml-4">
          {job.visibility === "private" && (
            <EyeOff size={16} className="text-gray-400" />
          )}
          {job.visibility === "anonymous" && (
            <Eye size={16} className="text-gray-400" />
          )}
        </div>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <Users size={16} />
          <span>{displayName}</span>
        </div>
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <DollarSign size={16} />
          <span>
            {job.payment_type === "percentage"
              ? `${job.payment_percentage}% recurring`
              : formatCurrency(job.payment_fixed)}
          </span>
        </div>
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <Calendar size={16} />
          <span>{formatDate(job.created_at)}</span>
        </div>
        <div className="text-sm">
          <span className="px-2 py-1 bg-gray-100 rounded">{job.job_role}</span>
        </div>
      </div>

      <div className="flex justify-between items-center">
        <div className="flex gap-2">
          <button
            onClick={() => router.push(`/job/${job.id}`)}
            className="px-4 py-2 bg-black text-white rounded hover:bg-gray-800 transition-colors text-sm"
          >
            View Details
          </button>
          {job.visibility !== "private" && (
            <button
              onClick={handleShare}
              className="px-4 py-2 border rounded hover:bg-gray-50 transition-colors text-sm flex items-center gap-2"
            >
              <Share2 size={16} />
              {copied ? "Copied!" : "Share"}
            </button>
          )}
        </div>
        <div className="text-sm text-gray-500">
          {job.applications_count || 0} applications
        </div>
      </div>
    </div>
  );
}
